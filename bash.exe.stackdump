Stack trace:
Frame         Function      Args
0007FFFFA100  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFA100, 0007FFFF9000) msys-2.0.dll+0x1FE8E
0007FFFFA100  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA3D8) msys-2.0.dll+0x67F9
0007FFFFA100  000210046832 (000210286019, 0007FFFF9FB8, 0007FFFFA100, 000000000000) msys-2.0.dll+0x6832
0007FFFFA100  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA100  000210068E24 (0007FFFFA110, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA3E0  00021006A225 (0007FFFFA110, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD22ED0000 ntdll.dll
7FFD21620000 KERNEL32.DLL
7FFD20630000 KERNELBASE.dll
7FFD20EF0000 USER32.dll
7FFD203F0000 win32u.dll
7FFD20CC0000 GDI32.dll
7FFD201E0000 gdi32full.dll
7FFD20420000 msvcp_win.dll
7FFD20AB0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD222F0000 advapi32.dll
7FFD20C00000 msvcrt.dll
7FFD20E40000 sechost.dll
7FFD20BD0000 bcrypt.dll
7FFD216F0000 RPCRT4.dll
7FFD1FA70000 CRYPTBASE.DLL
7FFD20370000 bcryptPrimitives.dll
7FFD21A10000 IMM32.DLL
